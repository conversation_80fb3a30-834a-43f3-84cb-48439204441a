[project]
name = "trader"
version = "0.1.0"
description = "Trading bot para Mercado Bitcoin"
authors = [
    {name = "Your Name", email = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.8"
dependencies = [
    "requests>=2.31.0",
    "python-dotenv>=1.0.0",
    "colorama>=0.4.6",
    "tenacity>=9.0.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.3",
    "pytest-mock>=3.12.0",
    "pytest-cov>=4.1.0",
    "ruff>=0.1.0",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["trader"]



[tool.pytest.ini_options]
testpaths = ["trader/tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = ["-v", "--tb=short"]

# Configuração do Ruff
[tool.ruff]
# Mesmo comprimento de linha que o Black
line-length = 88

# Assume Python 3.8+
target-version = "py38"

# Exclui arquivos/diretórios
exclude = [
    ".bzr",
    ".direnv",
    ".eggs",
    ".git",
    ".git-rewrite",
    ".hg",
    ".mypy_cache",
    ".nox",
    ".pants.d",
    ".pytype",
    ".ruff_cache",
    ".svn",
    ".tox",
    ".venv",
    "__pypackages__",
    "_build",
    "buck-out",
    "build",
    "dist",
    "node_modules",
    "venv",
]

# Configuração do linter
[tool.ruff.lint]
# Habilita regras de linting
select = [
    "E",   # pycodestyle errors
    "W",   # pycodestyle warnings
    "F",   # Pyflakes
    "I",   # isort
    "B",   # flake8-bugbear
    "C4",  # flake8-comprehensions
    "UP",  # pyupgrade
]

# Ignora regras específicas se necessário
ignore = [
    "E501",  # line too long, handled by black
    "B008",  # do not perform function calls in argument defaults
]

# Configuração do formatter
[tool.ruff.format]
# Como o black, usa aspas duplas
quote-style = "double"

# Como o black, indenta com espaços
indent-style = "space"

# Como o black, respeita magic trailing commas
skip-magic-trailing-comma = false

# Como o black, quebra automaticamente strings longas
line-ending = "auto"

# Configuração do isort (importações)
[tool.ruff.lint.isort]
known-first-party = ["trader"]


